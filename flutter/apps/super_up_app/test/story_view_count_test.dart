import 'package:flutter_test/flutter_test.dart';
import 'package:super_up/app/core/models/story/story_view_count_model.dart';
import 'package:super_up/app/core/models/story/story_model.dart';
import 'package:super_up/app/core/utils/enums.dart';

void main() {
  group('Story View Count Tests', () {
    test('StoryViewCountModel should parse from map correctly', () {
      final map = {'viewsCount': 5};
      final model = StoryViewCountModel.fromMap(map);
      
      expect(model.viewsCount, 5);
    });

    test('StoryViewCountModel should handle null viewsCount', () {
      final map = <String, dynamic>{};
      final model = StoryViewCountModel.fromMap(map);
      
      expect(model.viewsCount, 0);
    });

    test('StoryModel should include viewsCount field', () {
      final map = {
        '_id': 'story123',
        'userId': 'user123',
        'content': 'Test story',
        'expireAt': '2024-01-01T00:00:00.000Z',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'storyType': 'text',
        'fontType': 'normal',
        'viewedByMe': false,
        'viewsCount': 10,
      };
      
      final story = StoryModel.fromMap(map);
      
      expect(story.viewsCount, 10);
      expect(story.id, 'story123');
      expect(story.userId, 'user123');
    });

    test('StoryModel should handle null viewsCount', () {
      final map = {
        '_id': 'story123',
        'userId': 'user123',
        'content': 'Test story',
        'expireAt': '2024-01-01T00:00:00.000Z',
        'createdAt': '2024-01-01T00:00:00.000Z',
        'updatedAt': '2024-01-01T00:00:00.000Z',
        'storyType': 'text',
        'fontType': 'normal',
        'viewedByMe': false,
      };
      
      final story = StoryModel.fromMap(map);
      
      expect(story.viewsCount, null);
    });

    test('StoryModel toMap should include viewsCount', () {
      final story = StoryModel(
        id: 'story123',
        userId: 'user123',
        content: 'Test story',
        expireAt: '2024-01-01T00:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        storyType: StoryType.text,
        fontType: StoryFontType.normal,
        viewedByMe: false,
        viewsCount: 15,
      );
      
      final map = story.toMap();
      
      expect(map['viewsCount'], 15);
      expect(map['_id'], 'story123');
    });
  });
}
