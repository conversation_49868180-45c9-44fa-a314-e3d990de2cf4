import 'package:dashed_circle/dashed_circle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/core/models/story/story_model.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';
import 'package:s_translation/generated/l10n.dart';

class StoryWidget extends StatelessWidget {
  final UserStoryModel storyModel;
  final Function(UserStoryModel storyModel) onTap;
  final Function(UserStoryModel storyModel) onLongTap;
  final VoidCallback? toCreateStory;
  final bool isMe;
  final bool isLoading;
  final bool isViewed;

  const StoryWidget({
    super.key,
    required this.storyModel,
    required this.onTap,
    required this.onLongTap,
    this.toCreateStory,
    this.isMe = false,
    this.isLoading = false,
    this.isViewed = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onLongPress: () {
        onLongTap(storyModel);
      },
      onTap: () {
        onTap(storyModel);
      },
      dense: false,
      contentPadding: const EdgeInsets.all(0),
      leading: Stack(
        children: [
          DashedCircle(
            dashes: storyModel.stories.length,
            color:
                isMe ? Colors.green : (isViewed ? Colors.grey : Colors.green),
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: isLoading
                  ? const CupertinoActivityIndicator(
                      radius: 15,
                    )
                  : VCircleAvatar(
                      radius: 25,
                      vFileSource: VPlatformFile.fromUrl(
                        networkUrl: storyModel.userData.userImage,
                      ),
                    ),
            ),
          ),
          if (isMe && !isLoading)
            PositionedDirectional(
              end: 0,
              bottom: 0,
              child: InkWell(
                onTap: toCreateStory,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.add,
                    size: 22,
                    color: Colors.white,
                  ),
                ),
              ),
            )
        ],
      ),
      title: Text(
        storyModel.userData.fullName,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: isMe
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).addNewStory,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                if (storyModel.stories.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      const Icon(
                        Icons.visibility,
                        size: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getTotalViewsText(),
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            )
          : Text(
              format(
                DateTime.parse(storyModel.stories.last.createdAt).toLocal(),
                locale: Localizations.localeOf(context).languageCode,
              ),
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
    );
  }

  String _getTotalViewsText() {
    if (storyModel.stories.isEmpty) return "0 views";

    int totalViews = 0;
    for (final story in storyModel.stories) {
      totalViews += story.viewsCount ?? 0;
    }

    return totalViews == 1 ? "1 view" : "$totalViews views";
  }
}
